<?php

/**
 * <PERSON><PERSON><PERSON> to move images from storage and uploads to public/images
 */

echo "Moving images from storage and uploads to public/images...\n";

// Define source and target directories
$moves = [
    // News images
    [
        'source' => __DIR__ . '/storage/app/public/news',
        'target' => __DIR__ . '/public/images/news',
        'type' => 'news'
    ],
    [
        'source' => __DIR__ . '/../uploads/images/news',
        'target' => __DIR__ . '/public/images/news',
        'type' => 'news (uploads)'
    ],
    [
        'source' => __DIR__ . '/../frontend/public/images/news',
        'target' => __DIR__ . '/public/images/news',
        'type' => 'news (frontend)'
    ],
    
    // Gallery images
    [
        'source' => __DIR__ . '/storage/app/public/gallery',
        'target' => __DIR__ . '/public/images/gallery',
        'type' => 'gallery'
    ],
    [
        'source' => __DIR__ . '/../uploads/images/gallery',
        'target' => __DIR__ . '/public/images/gallery',
        'type' => 'gallery (uploads)'
    ],
    [
        'source' => __DIR__ . '/../frontend/public/images/gallery',
        'target' => __DIR__ . '/public/images/gallery',
        'type' => 'gallery (frontend)'
    ],
    
    // General uploads/images (could be news or gallery)
    [
        'source' => __DIR__ . '/../uploads/images',
        'target' => __DIR__ . '/public/images/news',
        'type' => 'general uploads (to news)',
        'filter' => ['news_', 'temp_']
    ]
];

$totalMoved = 0;
$totalErrors = 0;

foreach ($moves as $move) {
    $sourceDir = $move['source'];
    $targetDir = $move['target'];
    $type = $move['type'];
    $filter = $move['filter'] ?? null;
    
    echo "\n--- Processing {$type} ---\n";
    
    // Check if source directory exists
    if (!file_exists($sourceDir)) {
        echo "Source directory does not exist: $sourceDir\n";
        continue;
    }
    
    // Create target directory if it doesn't exist
    if (!file_exists($targetDir)) {
        mkdir($targetDir, 0755, true);
        echo "Created target directory: $targetDir\n";
    }
    
    // Get all files from source directory
    $files = glob($sourceDir . '/*');
    $movedCount = 0;
    $errorCount = 0;
    
    foreach ($files as $file) {
        if (is_file($file)) {
            $filename = basename($file);
            
            // Apply filter if specified
            if ($filter) {
                $matchesFilter = false;
                foreach ($filter as $filterPattern) {
                    if (strpos($filename, $filterPattern) !== false) {
                        $matchesFilter = true;
                        break;
                    }
                }
                if (!$matchesFilter) {
                    continue;
                }
            }
            
            $targetFile = $targetDir . '/' . $filename;
            
            // Check if target file already exists
            if (file_exists($targetFile)) {
                echo "File already exists in target: $filename (skipping)\n";
                continue;
            }
            
            // Copy file to target location
            if (copy($file, $targetFile)) {
                echo "Moved: $filename\n";
                $movedCount++;
                $totalMoved++;
                
                // Remove original file after successful copy
                unlink($file);
            } else {
                echo "Error moving: $filename\n";
                $errorCount++;
                $totalErrors++;
            }
        }
    }
    
    echo "Files moved from {$type}: $movedCount\n";
    echo "Errors in {$type}: $errorCount\n";
}

echo "\n=== SUMMARY ===\n";
echo "Total files moved: $totalMoved\n";
echo "Total errors: $totalErrors\n";

// Update database URLs
echo "\nUpdating database URLs...\n";

try {
    // Include Laravel bootstrap
    require_once __DIR__ . '/vendor/autoload.php';
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    // Update news URLs
    $newsUpdated = DB::table('news')
        ->where('image_url', 'like', '%/uploads/images/%')
        ->orWhere('image_url', 'like', '%/images/news/%')
        ->update([
            'image_url' => DB::raw("CONCAT('http://localhost:8000/images/news/', SUBSTRING_INDEX(image_url, '/', -1))")
        ]);
    
    echo "Updated $newsUpdated news records\n";
    
    // Update gallery URLs
    $galleryUpdated = DB::table('galleries')
        ->where('image_url', 'like', '%/uploads/images/%')
        ->orWhere('image_url', 'like', '%/images/gallery/%')
        ->update([
            'image_url' => DB::raw("CONCAT('http://localhost:8000/images/gallery/', SUBSTRING_INDEX(image_url, '/', -1))")
        ]);
    
    echo "Updated $galleryUpdated gallery records\n";
    
} catch (Exception $e) {
    echo "Error updating database: " . $e->getMessage() . "\n";
}

echo "\nDone!\n";
?>
