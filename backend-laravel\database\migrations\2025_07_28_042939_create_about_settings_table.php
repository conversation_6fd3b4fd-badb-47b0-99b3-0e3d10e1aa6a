<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('about_settings', function (Blueprint $table) {
            $table->id();
            $table->string('section_key')->unique(); // 'vision', 'mission', 'history', 'values'
            $table->string('title');
            $table->text('content');
            $table->text('description')->nullable();
            $table->string('icon')->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->json('meta_data')->nullable(); // For additional data like images, links, etc.
            $table->timestamps();
        });

        // Insert default about sections
        DB::table('about_settings')->insert([
            [
                'section_key' => 'vision',
                'title' => 'Visi Sekolah',
                'content' => '<PERSON><PERSON><PERSON> sekolah unggul yang menghasilkan lulusan berka<PERSON>, berp<PERSON><PERSON>, dan berwawasan global.',
                'description' => 'Visi sekolah yang menggambarkan cita-cita dan tujuan jangka panjang.',
                'icon' => 'eye',
                'sort_order' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'section_key' => 'mission',
                'title' => 'Misi Sekolah',
                'content' => '1. Menyelenggarakan pendidikan berkualitas dengan kurikulum yang relevan\n2. Mengembangkan karakter siswa melalui kegiatan ekstrakurikuler\n3. Membangun lingkungan belajar yang kondusif dan inovatif\n4. Meningkatkan kompetensi tenaga pendidik dan kependidikan\n5. Menjalin kerjasama dengan berbagai pihak untuk kemajuan sekolah',
                'description' => 'Misi sekolah sebagai langkah konkret untuk mencapai visi.',
                'icon' => 'target',
                'sort_order' => 2,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'section_key' => 'history',
                'title' => 'Sejarah Sekolah',
                'content' => 'Sekolah ini didirikan pada tahun 1985 dengan visi untuk memberikan pendidikan berkualitas kepada generasi muda. Dimulai dengan 3 kelas dan 60 siswa, kini telah berkembang menjadi institusi pendidikan terkemuka dengan fasilitas modern dan prestasi yang membanggakan.',
                'description' => 'Perjalanan sejarah dan perkembangan sekolah dari masa ke masa.',
                'icon' => 'book-open',
                'sort_order' => 3,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'section_key' => 'values',
                'title' => 'Nilai-Nilai Sekolah',
                'content' => 'INTEGRITAS - Jujur, dapat dipercaya, dan konsisten\nEKSELENSI - Selalu berusaha memberikan yang terbaik\nINOVASI - Kreatif dan adaptif terhadap perubahan\nKOLABORASI - Bekerja sama untuk mencapai tujuan bersama\nPEDULI - Memperhatikan lingkungan dan sesama',
                'description' => 'Nilai-nilai fundamental yang menjadi pedoman dalam setiap aktivitas sekolah.',
                'icon' => 'heart',
                'sort_order' => 4,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('about_settings');
    }
};
