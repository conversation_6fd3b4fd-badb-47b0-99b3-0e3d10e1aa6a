<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SocialMedia extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'social_media';

    protected $fillable = [
        'platform',
        'name',
        'url',
        'icon_class',
        'is_active',
        'sort_order',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * Get the user who created this social media.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this social media.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope to get only active social media.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'desc');
    }

    /**
     * Get formatted platform name.
     */
    public function getFormattedPlatformAttribute()
    {
        return ucfirst($this->platform);
    }

    /**
     * Get icon HTML.
     */
    public function getIconHtmlAttribute()
    {
        if ($this->icon_class) {
            return '<i class="' . $this->icon_class . '"></i>';
        }
        return '';
    }

    /**
     * Check if URL is valid.
     */
    public function isValidUrl()
    {
        return filter_var($this->url, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * Get platform color based on platform type.
     */
    public function getPlatformColorAttribute()
    {
        $colors = [
            'facebook' => '#1877f2',
            'instagram' => '#e4405f',
            'twitter' => '#1da1f2',
            'youtube' => '#ff0000',
            'linkedin' => '#0077b5',
            'tiktok' => '#000000',
            'whatsapp' => '#25d366',
            'telegram' => '#0088cc'
        ];

        return $colors[$this->platform] ?? '#6b7280';
    }
}
