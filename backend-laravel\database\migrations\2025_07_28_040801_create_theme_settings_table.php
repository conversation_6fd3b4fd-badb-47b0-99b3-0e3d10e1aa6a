<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('theme_settings', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('primary_color')->default('#3b82f6');
            $table->string('secondary_color')->default('#64748b');
            $table->string('accent_color')->default('#10b981');
            $table->string('background_color')->default('#ffffff');
            $table->string('text_color')->default('#1f2937');
            $table->string('sidebar_color')->default('#1f2937');
            $table->string('navbar_color')->default('#ffffff');
            $table->string('card_color')->default('#ffffff');
            $table->string('border_color')->default('#e5e7eb');
            $table->json('custom_css')->nullable();
            $table->json('font_settings')->nullable();
            $table->boolean('is_active')->default(false);
            $table->boolean('is_default')->default(false);
            $table->text('description')->nullable();
            $table->timestamps();
        });

        // Insert default theme
        DB::table('theme_settings')->insert([
            'name' => 'Default Theme',
            'primary_color' => '#3b82f6',
            'secondary_color' => '#64748b',
            'accent_color' => '#10b981',
            'background_color' => '#ffffff',
            'text_color' => '#1f2937',
            'sidebar_color' => '#1f2937',
            'navbar_color' => '#ffffff',
            'card_color' => '#ffffff',
            'border_color' => '#e5e7eb',
            'custom_css' => null,
            'font_settings' => null,
            'is_active' => true,
            'is_default' => true,
            'description' => 'Default theme for the school management system',
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('theme_settings');
    }
};
