import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { <PERSON><PERSON>, Card } from '../../components/ui';
import { FadeIn } from '../../components/ui/AnimatedComponents';
import AdminLayout from '../../components/admin/AdminLayout';
import { api } from '../../services/api';
import { useAlert } from '../../contexts/AlertContext';

const SocialMediaForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);
  const { showAlert } = useAlert();

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    platform: '',
    name: '',
    url: '',
    icon_class: '',
    is_active: true,
    sort_order: 0
  });

  // Predefined social media platforms
  const platformOptions = [
    { value: 'facebook', label: 'Facebook', icon: 'fab fa-facebook-f', color: '#1877f2' },
    { value: 'instagram', label: 'Instagram', icon: 'fab fa-instagram', color: '#e4405f' },
    { value: 'twitter', label: 'Twitter', icon: 'fab fa-twitter', color: '#1da1f2' },
    { value: 'youtube', label: 'YouTube', icon: 'fab fa-youtube', color: '#ff0000' },
    { value: 'linkedin', label: 'LinkedIn', icon: 'fab fa-linkedin-in', color: '#0077b5' },
    { value: 'tiktok', label: 'TikTok', icon: 'fab fa-tiktok', color: '#000000' },
    { value: 'whatsapp', label: 'WhatsApp', icon: 'fab fa-whatsapp', color: '#25d366' },
    { value: 'telegram', label: 'Telegram', icon: 'fab fa-telegram-plane', color: '#0088cc' }
  ];

  // Fetch data for edit mode
  useEffect(() => {
    if (isEdit) {
      fetchSocialMedia();
    }
  }, [id, isEdit]);

  const fetchSocialMedia = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/admin/social-media/${id}`);
      if (response.data.success) {
        const data = response.data.data;
        setFormData({
          platform: data.platform || '',
          name: data.name || '',
          url: data.url || '',
          icon_class: data.icon_class || '',
          is_active: data.is_active || false,
          sort_order: data.sort_order || 0
        });
      }
    } catch (err) {
      console.error('Error fetching social media:', err);
      showAlert('error', 'Gagal memuat data sosial media');
      navigate('/admin/social-media');
    } finally {
      setLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (type === 'checkbox') {
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
      
      // Auto-fill icon class when platform is selected
      if (name === 'platform') {
        const platform = platformOptions.find(p => p.value === value);
        if (platform) {
          setFormData(prev => ({ 
            ...prev, 
            name: platform.label,
            icon_class: platform.icon 
          }));
        }
      }
    }
  };

  // Handle form submit
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      let response;
      if (isEdit) {
        response = await api.put(`/admin/social-media/${id}`, formData);
      } else {
        response = await api.post('/admin/social-media', formData);
      }

      if (response.data.success) {
        showAlert('success', isEdit ? 'Sosial media berhasil diupdate!' : 'Sosial media berhasil ditambahkan!');
        navigate('/admin/social-media');
      }
    } catch (err) {
      console.error('Error submitting form:', err);
      showAlert('error', 'Terjadi kesalahan: ' + (err.response?.data?.message || err.message));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/social-media');
  };

  if (loading && isEdit) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="p-6">
        <FadeIn direction="down" className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {isEdit ? 'Edit Sosial Media' : 'Tambah Sosial Media'}
              </h2>
              <p className="text-gray-600 mt-1">
                {isEdit ? 'Ubah informasi sosial media' : 'Tambahkan sosial media baru'}
              </p>
            </div>
            <Button onClick={handleCancel} variant="outline">
              Kembali
            </Button>
          </div>
        </FadeIn>

        <FadeIn>
          <Card>
            <div className="p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Platform */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Platform *
                    </label>
                    <select
                      name="platform"
                      value={formData.platform}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                      disabled={loading}
                    >
                      <option value="">Pilih Platform</option>
                      {platformOptions.map(platform => (
                        <option key={platform.value} value={platform.value}>
                          {platform.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Sort Order */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Urutan
                    </label>
                    <input
                      type="number"
                      name="sort_order"
                      value={formData.sort_order}
                      onChange={handleInputChange}
                      min="0"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={loading}
                    />
                  </div>
                </div>

                {/* Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nama *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Masukkan nama sosial media"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                    disabled={loading}
                  />
                </div>

                {/* URL */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    URL *
                  </label>
                  <input
                    type="url"
                    name="url"
                    value={formData.url}
                    onChange={handleInputChange}
                    placeholder="https://..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                    disabled={loading}
                  />
                </div>

                {/* Icon Class */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Icon Class
                  </label>
                  <input
                    type="text"
                    name="icon_class"
                    value={formData.icon_class}
                    onChange={handleInputChange}
                    placeholder="fab fa-facebook-f"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={loading}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Gunakan Font Awesome icon class (contoh: fab fa-facebook-f)
                  </p>
                </div>

                {/* Is Active */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_active"
                    checked={formData.is_active}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    disabled={loading}
                  />
                  <label className="ml-2 block text-sm text-gray-700">
                    Aktifkan sosial media ini
                  </label>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                  <Button 
                    type="button" 
                    onClick={handleCancel} 
                    variant="outline"
                    disabled={loading}
                  >
                    Batal
                  </Button>
                  <Button 
                    type="submit" 
                    variant="primary"
                    disabled={loading}
                  >
                    {loading ? 'Menyimpan...' : (isEdit ? 'Update' : 'Tambah')}
                  </Button>
                </div>
              </form>
            </div>
          </Card>
        </FadeIn>
      </div>
    </AdminLayout>
  );
};

export default SocialMediaForm;
