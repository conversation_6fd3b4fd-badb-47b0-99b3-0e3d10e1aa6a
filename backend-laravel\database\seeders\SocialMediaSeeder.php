<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SocialMedia;

class SocialMediaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $socialMediaData = [
            [
                'platform' => 'facebook',
                'name' => 'Facebook',
                'url' => 'https://facebook.com/sman1jakarta',
                'icon_class' => 'fab fa-facebook-f',
                'is_active' => true,
                'sort_order' => 1,
                'created_by' => 1
            ],
            [
                'platform' => 'instagram',
                'name' => 'Instagram',
                'url' => 'https://instagram.com/sman1jakarta',
                'icon_class' => 'fab fa-instagram',
                'is_active' => true,
                'sort_order' => 2,
                'created_by' => 1
            ],
            [
                'platform' => 'youtube',
                'name' => 'YouTube',
                'url' => 'https://youtube.com/@sman1jakarta',
                'icon_class' => 'fab fa-youtube',
                'is_active' => true,
                'sort_order' => 3,
                'created_by' => 1
            ],
            [
                'platform' => 'twitter',
                'name' => 'Twitter',
                'url' => 'https://twitter.com/sman1jakarta',
                'icon_class' => 'fab fa-twitter',
                'is_active' => true,
                'sort_order' => 4,
                'created_by' => 1
            ]
        ];

        foreach ($socialMediaData as $data) {
            SocialMedia::create($data);
        }
    }
}
