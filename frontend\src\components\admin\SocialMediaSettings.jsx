import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, Card } from '../ui';
import { FadeIn, StaggerContainer, StaggerItem } from '../ui/AnimatedComponents';
import { api } from '../../services/api';
import { useAlert } from '../../contexts/AlertContext';

const SocialMediaSettings = () => {
  const navigate = useNavigate();
  const [socialMedia, setSocialMedia] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { showAlert } = useAlert();

  // Predefined social media platforms for display
  const platformOptions = [
    { value: 'facebook', label: 'Facebook', icon: 'fab fa-facebook-f', color: '#1877f2' },
    { value: 'instagram', label: 'Instagram', icon: 'fab fa-instagram', color: '#e4405f' },
    { value: 'twitter', label: 'Twitter', icon: 'fab fa-twitter', color: '#1da1f2' },
    { value: 'youtube', label: 'YouTube', icon: 'fab fa-youtube', color: '#ff0000' },
    { value: 'linkedin', label: 'LinkedIn', icon: 'fab fa-linkedin-in', color: '#0077b5' },
    { value: 'tiktok', label: 'TikTok', icon: 'fab fa-tiktok', color: '#000000' },
    { value: 'whatsapp', label: 'WhatsApp', icon: 'fab fa-whatsapp', color: '#25d366' },
    { value: 'telegram', label: 'Telegram', icon: 'fab fa-telegram-plane', color: '#0088cc' }
  ];

  // Fetch social media settings
  const fetchSocialMedia = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.get('/admin/social-media');
      if (response.data.success) {
        setSocialMedia(response.data.data);
      } else {
        setError('Gagal memuat pengaturan sosial media');
      }
    } catch (err) {
      console.error('Error fetching social media:', err);
      setError('Terjadi kesalahan saat memuat data: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSocialMedia();
  }, []);



  // Handle navigation
  const handleCreate = () => {
    navigate('/admin/social-media/create');
  };

  const handleEdit = (social) => {
    navigate(`/admin/social-media/edit/${social.id}`);
  };

  // Handle delete
  const handleDelete = async (social) => {
    if (window.confirm(`Apakah Anda yakin ingin menghapus sosial media "${social.name}"?`)) {
      try {
        const response = await api.delete(`/admin/social-media/${social.id}`);
        if (response.data.success) {
          await fetchSocialMedia();
          showAlert('success', 'Sosial media berhasil dihapus!');
        }
      } catch (err) {
        console.error('Error deleting social media:', err);
        showAlert('error', 'Terjadi kesalahan: ' + (err.response?.data?.message || err.message));
      }
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <FadeIn direction="down" className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Pengaturan Sosial Media</h2>
          <p className="text-gray-600 mt-1">Kelola link sosial media yang ditampilkan di navbar</p>
        </div>
        <Button
          onClick={handleCreate}
          variant="primary"
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          }
        >
          Tambah Sosial Media
        </Button>
      </FadeIn>

      {/* Error Message */}
      {error && (
        <FadeIn className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </FadeIn>
      )}



      {/* Social Media List */}
      <StaggerContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {socialMedia.map((social, index) => (
          <StaggerItem key={social.id} delay={index * 0.1}>
            <Card className="h-full">
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                      <i className={`${social.icon_class} text-lg`} style={{ color: platformOptions.find(p => p.value === social.platform)?.color || '#6b7280' }}></i>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{social.name}</h3>
                      <p className="text-sm text-gray-500 capitalize">{social.platform}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      social.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {social.is_active ? 'Aktif' : 'Nonaktif'}
                    </span>
                  </div>
                </div>

                <div className="mb-4">
                  <p className="text-sm text-gray-600 break-all">{social.url}</p>
                </div>

                <div className="flex justify-between items-center pt-4 border-t">
                  <span className="text-xs text-gray-500">Order: {social.sort_order}</span>
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => handleEdit(social)}
                      variant="outline"
                      size="sm"
                    >
                      Edit
                    </Button>
                    <Button
                      onClick={() => handleDelete(social)}
                      variant="outline"
                      size="sm"
                      className="text-red-600 hover:text-red-700"
                    >
                      Hapus
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </StaggerItem>
        ))}
      </StaggerContainer>


    </div>
  );
};

export default SocialMediaSettings;
