import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { <PERSON><PERSON>, Card } from '../../components/ui';
import { FadeIn } from '../../components/ui/AnimatedComponents';
import AdminLayout from '../../components/admin/AdminLayout';
import { api } from '../../services/api';
import { useAlert } from '../../contexts/AlertContext';

const GalleryForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);
  const { showAlert } = useAlert();

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    image: null,
    is_featured: false
  });

  // Predefined categories
  const categoryOptions = [
    { value: 'kegiatan', label: 'Kegiatan Sekolah' },
    { value: 'fasilitas', label: 'Fasilitas' },
    { value: 'prestasi', label: 'Prestasi' },
    { value: 'akademik', label: 'Akademik' },
    { value: 'ekstrakurikuler', label: 'Ekstrakurikuler' },
    { value: 'acara', label: 'Acara Khusus' }
  ];

  // Fetch data for edit mode
  useEffect(() => {
    if (isEdit) {
      fetchGallery();
    }
  }, [id, isEdit]);

  const fetchGallery = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/admin/gallery/${id}`);
      if (response.data.success) {
        const data = response.data.data;
        setFormData({
          title: data.title || '',
          description: data.description || '',
          category: data.category || '',
          image: null,
          is_featured: data.is_featured || false
        });
      }
    } catch (err) {
      console.error('Error fetching gallery:', err);
      showAlert('error', 'Gagal memuat data galeri');
      navigate('/admin/gallery');
    } finally {
      setLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked, files } = e.target;
    
    if (type === 'checkbox') {
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'file') {
      setFormData(prev => ({ ...prev, [name]: files[0] }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle form submit
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const submitData = new FormData();
      submitData.append('title', formData.title);
      submitData.append('description', formData.description);
      submitData.append('category', formData.category);
      submitData.append('is_featured', formData.is_featured ? '1' : '0');
      
      if (formData.image) {
        submitData.append('image', formData.image);
      }

      let response;
      if (isEdit) {
        submitData.append('_method', 'PUT');
        response = await api.post(`/admin/gallery/${id}`, submitData);
      } else {
        response = await api.post('/admin/gallery', submitData);
      }

      if (response.data.success) {
        showAlert('success', isEdit ? 'Galeri berhasil diupdate!' : 'Galeri berhasil ditambahkan!');
        navigate('/admin/gallery');
      }
    } catch (err) {
      console.error('Error submitting form:', err);
      showAlert('error', 'Terjadi kesalahan: ' + (err.response?.data?.message || err.message));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/gallery');
  };

  if (loading && isEdit) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="p-6">
        <FadeIn direction="down" className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {isEdit ? 'Edit Galeri' : 'Tambah Galeri'}
              </h2>
              <p className="text-gray-600 mt-1">
                {isEdit ? 'Ubah informasi galeri' : 'Tambahkan foto baru ke galeri'}
              </p>
            </div>
            <Button onClick={handleCancel} variant="outline">
              Kembali
            </Button>
          </div>
        </FadeIn>

        <FadeIn>
          <Card>
            <div className="p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Title */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Judul *
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="Masukkan judul foto"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                    disabled={loading}
                  />
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Category */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Kategori *
                    </label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                      disabled={loading}
                    >
                      <option value="">Pilih Kategori</option>
                      {categoryOptions.map(category => (
                        <option key={category.value} value={category.value}>
                          {category.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Image */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Gambar *
                    </label>
                    <input
                      type="file"
                      name="image"
                      onChange={handleInputChange}
                      accept="image/*"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required={!isEdit}
                      disabled={loading}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Format: JPG, PNG, GIF. Maksimal 5MB.
                    </p>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Deskripsi
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={4}
                    placeholder="Masukkan deskripsi foto (opsional)"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={loading}
                  />
                </div>

                {/* Is Featured */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_featured"
                    checked={formData.is_featured}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    disabled={loading}
                  />
                  <label className="ml-2 block text-sm text-gray-700">
                    Jadikan foto unggulan
                  </label>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                  <Button 
                    type="button" 
                    onClick={handleCancel} 
                    variant="outline"
                    disabled={loading}
                  >
                    Batal
                  </Button>
                  <Button 
                    type="submit" 
                    variant="primary"
                    disabled={loading}
                  >
                    {loading ? 'Menyimpan...' : (isEdit ? 'Update' : 'Tambah')}
                  </Button>
                </div>
              </form>
            </div>
          </Card>
        </FadeIn>
      </div>
    </AdminLayout>
  );
};

export default GalleryForm;
