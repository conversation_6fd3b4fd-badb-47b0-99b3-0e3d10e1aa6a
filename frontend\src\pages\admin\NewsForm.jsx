import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { <PERSON>ton, Card } from '../../components/ui';
import { FadeIn } from '../../components/ui/AnimatedComponents';
import AdminLayout from '../../components/admin/AdminLayout';
import { api } from '../../services/api';
import { useAlert } from '../../contexts/AlertContext';

const NEWS_CATEGORIES = [
  { value: 'akademik', label: 'Akademik' },
  { value: 'kegiatan-sekolah', label: 'Kegiatan Sekolah' },
  { value: 'prestasi', label: 'Prestasi' },
  { value: 'pengumuman', label: 'Pengumuman' },
  { value: 'ekstrakurikuler', label: 'Ekstrakurikuler' },
  { value: 'berita-umum', label: 'Berita Umum' }
];

const NEWS_CATEGORIES = [
  { value: 'akademik', label: 'Akademik' },
  { value: 'kegiatan-sekolah', label: 'Kegiatan Sekolah' },
  { value: 'prestasi', label: 'Prestasi' },
  { value: 'pengumuman', label: 'Pengumuman' },
  { value: 'ekstrakurikuler', label: 'Ekstrakurikuler' },
  { value: 'berita-umum', label: 'Berita Umum' }
];

const NewsForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);
  const { showAlert } = useAlert();

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    excerpt: '',
    category: '',  // Added category field
    image: null,
    is_published: true,
    published_at: ''
  });

  // Fetch data for edit mode
  useEffect(() => {
    if (isEdit) {
      fetchNews();
    }
  }, [id, isEdit]);

  const fetchNews = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/admin/news/${id}`);
      if (response.data.success) {
        const data = response.data.data;
        setFormData({
          title: data.title || '',
          content: data.content || '',
          excerpt: data.excerpt || '',
          category: data.category || '',
          image: null,
          is_published: data.is_published || false,
          published_at: data.published_at ? data.published_at.split('T')[0] : ''
        });
      }
    } catch (err) {
      console.error('Error fetching news:', err);
      showAlert('error', 'Gagal memuat data berita');
      navigate('/admin/news');
    } finally {
      setLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked, files } = e.target;
    
    if (type === 'checkbox') {
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'file') {
      setFormData(prev => ({ ...prev, [name]: files[0] }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle form submit
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const submitData = new FormData();
      submitData.append('title', formData.title);
      submitData.append('content', formData.content);
      submitData.append('excerpt', formData.excerpt);
      submitData.append('category', formData.category);
      submitData.append('is_published', formData.is_published ? '1' : '0');
      if (formData.published_at) {
        submitData.append('published_at', formData.published_at);
      }
      if (formData.image) {
        submitData.append('image', formData.image);
      }

      let response;
      if (isEdit) {
        submitData.append('_method', 'PUT');
        response = await api.post(`/admin/news/${id}`, submitData);
      } else {
        response = await api.post('/admin/news', submitData);
      }

      if (response.data.success) {
        showAlert('success', isEdit ? 'Berita berhasil diupdate!' : 'Berita berhasil ditambahkan!');
        navigate('/admin/news');
      }
    } catch (err) {
      console.error('Error submitting form:', err);
      showAlert('error', 'Terjadi kesalahan: ' + (err.response?.data?.message || err.message));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/news');
  };

  if (loading && isEdit) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="p-6">
        <FadeIn direction="down" className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {isEdit ? 'Edit Berita' : 'Tambah Berita'}
              </h2>
              <p className="text-gray-600 mt-1">
                {isEdit ? 'Ubah informasi berita' : 'Tambahkan berita baru'}
              </p>
            </div>
            <Button onClick={handleCancel} variant="outline">
              Kembali
            </Button>
          </div>
        </FadeIn>

        <FadeIn>
          <Card>
            <div className="p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Title */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Judul *
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="Masukkan judul berita"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                    disabled={loading}
                  />
                </div>

                {/* Category */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Kategori *
                  </label>
                  <input
                    type="text"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    placeholder="Masukkan kategori berita"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                    disabled={loading}
                  />
                </div>

                {/* Excerpt */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ringkasan
                  </label>
                  <textarea
                    name="excerpt"
                    value={formData.excerpt}
                    onChange={handleInputChange}
                    rows={3}
                    placeholder="Masukkan ringkasan berita"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={loading}
                  />
                </div>

                {/* Content */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Konten *
                  </label>
                  <textarea
                    name="content"
                    value={formData.content}
                    onChange={handleInputChange}
                    rows={8}
                    placeholder="Masukkan konten berita"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                    disabled={loading}
                  />
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Image */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Gambar
                    </label>
                    <input
                      type="file"
                      name="image"
                      onChange={handleInputChange}
                      accept="image/*"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={loading}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Format: JPG, PNG, GIF. Maksimal 2MB.
                    </p>
                  </div>

                  {/* Published Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tanggal Publikasi
                    </label>
                    <input
                      type="date"
                      name="published_at"
                      value={formData.published_at}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={loading}
                    />
                  </div>
                </div>

                {/* Is Published */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_published"
                    checked={formData.is_published}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    disabled={loading}
                  />
                  <label className="ml-2 block text-sm text-gray-700">
                    Publikasikan berita ini
                  </label>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                  <Button 
                    type="button" 
                    onClick={handleCancel} 
                    variant="outline"
                    disabled={loading}
                  >
                    Batal
                  </Button>
                  <Button 
                    type="submit" 
                    variant="primary"
                    disabled={loading}
                  >
                    {loading ? 'Menyimpan...' : (isEdit ? 'Update' : 'Tambah')}
                  </Button>
                </div>
              </form>
            </div>
          </Card>
        </FadeIn>
      </div>
    </AdminLayout>
  );
};

export default NewsForm;
